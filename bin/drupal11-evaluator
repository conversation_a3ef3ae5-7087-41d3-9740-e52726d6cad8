#!/usr/bin/env php
<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Drupal\Evaluator\Command\AnalyzeCommand;
use Symfony\Component\Console\Application;

$application = new Application('Drupal 11 Evaluator', '1.0.0');
$application->add(new AnalyzeCommand());
$application->setDefaultCommand('analyze');
$application->run();

# Analyze only custom modules (static analysis)
#./bin/drupal11-evaluator analyze /path/to/site --custom-only --static-only

# Analyze only custom modules (with upgrade_status)
#./bin/drupal11-evaluator analyze /path/to/site --custom-only

# Normal analysis (both contrib and custom)
#./bin/drupal11-evaluator analyze /path/to/site
