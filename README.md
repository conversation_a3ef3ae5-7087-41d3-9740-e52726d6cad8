# Drupal 11 Readiness Evaluator

A comprehensive tool for evaluating Drupal 9/10 sites for Drupal 11 readiness, providing detailed upgrade audits for client quoting purposes.

## Features

- **Two-stage analysis**: Static code analysis and full Lando environment testing
- **Comprehensive module scanning**: Analyzes contrib and custom modules
- **Environment compatibility**: Checks PHP, Drush, and database versions
- **Detailed reporting**: Structured output in Markdown or JSON formats
- **Automated setup**: Handles Lando configuration and upgrade_status installation

## Installation

```bash
composer install
```

## Usage

### Basic Analysis (Static Only)

```bash
./bin/drupal11-evaluator analyze /path/to/drupal/site --static-only
```

### Full Analysis with Lando

For full analysis with upgrade_status scanning, you need to manually set up the Lando environment first:

```bash
# 1. Clone the repository
git clone https://github.com/client/drupal-site.git
cd drupal-site

# 2. Run vits_setup (if available)
./vits_setup

# 3. Ensure .lando.yml has unique site name to avoid conflicts
# Edit .lando.yml and change 'name' to something unique like 'client-d11-eval'

# 4. Start Lando
lando start

# 5. Download fresh database (if vits_copy_live available)
./vits_copy_live

# 6. Run the evaluator
../drupal11-evaluator/bin/drupal11-evaluator analyze .
```

### Options

- `--static-only, -s`: Perform static analysis only (no Lando)
- `--output, -o`: Save report to file
- `--format, -f`: Output format (`markdown` or `json`)
- `--work-dir, -w`: Working directory for cloned repos

### Examples

```bash
# Analyze local site with JSON output
./bin/drupal11-evaluator analyze /var/www/drupal --format=json --output=report.json

# Analyze remote repo with full Lando testing
./bin/drupal11-evaluator analyze https://github.com/example/site.git

# Quick static analysis
./bin/drupal11-evaluator analyze /path/to/site --static-only
```

## Report Output

The tool generates detailed reports including:

- **Environment Analysis**: PHP, Drush, database compatibility
- **Module Categorization**:
  - Already compatible modules
  - Modules requiring removal
  - Update requirements (minor/major versions)
  - Available patches
  - Problematic modules
- **Custom Module Assessment**: Version constraints, deprecated functions
- **Overall Assessment**: Complexity, effort estimates, risk factors

## Configuration

Edit `config/evaluator.yml` to customize:

- Target PHP/Drupal versions
- Lando configuration
- Custom module/theme paths
- Ignored modules
- Deprecated function list

## Requirements

- PHP 8.1+
- Composer
- Git
- Lando (for full analysis)
- Drush (installed via Lando)

## Development

```bash
# Install dependencies
composer install

# Run static analysis
./bin/drupal11-evaluator analyze /path/to/test/site --static-only

# Run full analysis
./bin/drupal11-evaluator analyze /path/to/test/site
```