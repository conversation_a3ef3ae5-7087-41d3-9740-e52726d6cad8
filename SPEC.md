# Drupal 11 Readiness Evaluator

## Overview
A two-stage evaluation system that assesses Drupal 9/10 sites for Drupal 11 readiness, providing detailed upgrade audits for client quoting purposes.

## Goals
- Automated evaluation of Drupal sites for D11 compatibility
- Generate detailed upgrade audit reports
- Support both static analysis (without Lando) and full environment testing
- Provide actionable recommendations with effort estimates

## Two-Stage Process

### Stage 1: Static Code Analysis (No Lando Required)
Fast preliminary assessment using codebase analysis:

**Inputs:**
- Git repository URL or local path
- Target Drupal version (default: 11)

**Analysis:**
- Parse `composer.json` for dependencies
- Scan module directories for custom modules
- Check PHP version compatibility
- Analyze database requirements from configuration
- Parse `.lando.yml` if present for environment details

**Outputs:**
- Basic compatibility overview
- List of modules requiring investigation
- PHP/database version requirements
- Estimated complexity score

### Stage 2: Full Environment Analysis (With Lando)
Complete assessment using live Drupal instance:

**Process:**
1. Clone repository
2. Start Lando environment with updated PHP (8.3+)
3. Update Drupal core to latest 10.4.x
4. Install/update `upgrade_status` module
5. Run comprehensive scans
6. Generate detailed report

## Output Specification

### Audit Report Structure

```
Upgrade Audit
Drupal version: [current_version]

Environment:
  PHP version: [version] - [status/recommendation]
  Drush version: [version] - [status/recommendation]  
  Database: [type/version] - [compatibility_notes]

Modules Analysis:

  Already compatible: [count]
  [list of modules with versions]

  Remove: [count]
  [list with removal reasons and special notes]
  
  Update to newer D11 compatible version: [count]
  [list with current → target versions]
  
  Major version changes: [count] - extra testing required
  [list with upgrade complexity notes]
  
  Patch for D11: [count]
  [list with patch status and community review notes]
  
  Problematic: [count]
  [list with detailed issues and recommendations]

Custom Modules: [count]
  [detailed analysis of each custom module with:]
  - Compatibility status
  - Required changes (version constraints, deprecated functions)
  - Effort estimate
  
Overall Assessment:
  - Complexity: [Low/Medium/High/Critical]
  - Estimated effort: [hours/days]
  - Critical blockers: [count]
  - Risk factors: [list]
```

### Module Classification Logic

**Already Compatible:**
- Latest stable release supports D11
- No deprecated API usage detected

**Remove:**
- Functionality moved to core
- Module abandoned/deprecated
- Better alternatives available

**Update Required:**
- Minor version update available with D11 support
- Major version update needed (requires testing)

**Patch Required:**
- Community patches available
- Patch quality assessment included

**Problematic:**
- No D11 version or patch available
- Significant deprecated API usage
- Module abandoned

**Custom Modules:**
- Scan for deprecated functions
- Check version constraints
- API compatibility analysis

## Technical Implementation

### Tools & Dependencies
- **PHP**: 8.1+ (for running the evaluator)
- **Composer**: For dependency analysis
- **Lando**: For environment management (stage 2)
- **Drush**: For Drupal operations
- **upgrade_status**: Drupal module for compatibility scanning

### Core Components

#### 1. Repository Handler
```php
interface RepositoryHandlerInterface {
    public function clone(string $repoUrl, string $targetDir): bool;
    public function analyze(string $path): RepositoryInfo;
}
```

#### 2. Static Analyzer
```php
interface StaticAnalyzerInterface {
    public function analyzeComposer(string $composerPath): ComposerAnalysis;
    public function scanCustomModules(string $modulesPath): array;
    public function checkPhpCompatibility(string $codebasePath): PhpCompatibility;
}
```

#### 3. Environment Manager
```php
interface EnvironmentManagerInterface {
    public function setupLando(string $projectPath): bool;
    public function updateDrupal(string $targetVersion): bool;
    public function installUpgradeStatus(): bool;
}
```

#### 4. Upgrade Status Scanner
```php
interface UpgradeStatusScannerInterface {
    public function scanContribModules(): ContribModuleReport;
    public function scanCustomModules(): CustomModuleReport;
    public function scanCore(): CoreCompatibilityReport;
}
```

#### 5. Report Generator
```php
interface ReportGeneratorInterface {
    public function generate(AnalysisResult $analysis): UpgradeReport;
    public function export(UpgradeReport $report, string $format): string;
}
```

### Data Structures

#### ModuleInfo
```php
class ModuleInfo {
    public string $name;
    public string $version;
    public string $type; // contrib|custom|core
    public CompatibilityStatus $d11Status;
    public ?string $d11Version;
    public array $issues;
    public ?PatchInfo $patch;
    public int $complexityScore;
}
```

#### CompatibilityStatus
```php
enum CompatibilityStatus {
    case COMPATIBLE;
    case UPDATE_REQUIRED;
    case MAJOR_UPDATE_REQUIRED;
    case PATCH_AVAILABLE;
    case REMOVE;
    case PROBLEMATIC;
    case UNKNOWN;
}
```

## Usage Examples

### CLI Interface
```bash
# Static analysis only
./drupal11-evaluator analyze --repo https://github.com/client/site.git --static-only

# Full analysis with Lando
./drupal11-evaluator analyze --repo https://github.com/client/site.git --full

# Local directory analysis
./drupal11-evaluator analyze --path /path/to/drupal/site --output report.json
```

### Configuration Options
```yaml
# evaluator.yml
php_version: "8.3"
drupal_target: "11.0"
lando:
  php_version: "8.3"
  database: "mysql:8.0"
custom_module_paths:
  - "docroot/modules/custom"
  - "docroot/themes/custom"
ignore_modules:
  - "devel"
  - "stage_file_proxy"
```

## Success Criteria
1. **Accuracy**: >95% accuracy in module compatibility detection
2. **Speed**: Static analysis completes in <2 minutes
3. **Completeness**: Covers all module types and common upgrade scenarios
4. **Usability**: Clear, actionable reports for non-technical stakeholders
5. **Reliability**: Handles edge cases gracefully (missing files, permission issues)

## Future Enhancements
- Integration with project management tools
- Historical tracking of upgrade progress
- Automated patch application testing
- Cost estimation based on module complexity
- CI/CD integration for continuous monitoring