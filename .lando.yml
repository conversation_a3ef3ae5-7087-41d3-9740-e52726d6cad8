
name: drupal11_evaluator

recipe: lamp

config:
  php: 8.3
  database: mariadb
  xdebug: true

services:
  appserver:
    scanner: false

  database:
    scanner: false
    creds:
      database: database
      user: databaseUser
      password: databasePassword
      host: database
      port: 3306

events:
  post-start:
    - appserver: if [ ! -f /usr/local/bin/acli ]; then echo "Installing acli ... "; curl -L -o /usr/local/bin/acli https://github.com/acquia/cli/releases/latest/download/acli.phar; chmod +x /usr/local/bin/acli; else echo "Updating acli ... "; /usr/local/bin/acli self:update;  fi

tooling:
  acli:
    service: appserver
    cmd: /usr/local/bin/acli

  acquia_usage:
    service: appserver
    cmd: php /app/acquia_usage.php
