<?php

namespace Drupal\Evaluator\Analyzer;

use <PERSON><PERSON><PERSON>\Evaluator\CompatibilityStatus;
use <PERSON><PERSON>al\Evaluator\Data\ComposerAnalysis;
use <PERSON><PERSON>al\Evaluator\Data\ModuleInfo;
use <PERSON><PERSON>al\Evaluator\Data\PhpCompatibility;
use <PERSON>upal\Evaluator\Interface\StaticAnalyzerInterface;
use Symfony\Component\Filesystem\Filesystem;

class StaticAnalyzer implements StaticAnalyzerInterface
{
    private Filesystem $filesystem;
    private array $deprecatedFunctions = [
        'watchdog_exception',
        'renderPlain',
        'drupal_set_message',
        'entity_load',
        'entity_load_multiple',
        'node_load',
        'user_load',
        'taxonomy_term_load',
        'file_load'
    ];

    private array $removeModules = [
        'ckeditor' => 'CKEditor 4 replaced by CKEditor 5 in core',
        'rdf' => 'RDF module removed from core',
        'hal' => 'HAL module removed from core',
        'color' => 'Color module removed from core',
        'quickedit' => 'Quick Edit removed from core'
    ];

    public function __construct()
    {
        $this->filesystem = new Filesystem();
    }

    public function analyzeComposer(string $composerPath): ComposerAnalysis
    {
        if (!$this->filesystem->exists($composerPath)) {
            return new ComposerAnalysis();
        }

        $composerContent = json_decode(file_get_contents($composerPath), true);
        $analysis = new ComposerAnalysis();

        if (isset($composerContent['require'])) {
            $analysis->dependencies = $composerContent['require'];
            
            foreach ($composerContent['require'] as $package => $version) {
                if (str_starts_with($package, 'drupal/')) {
                    $moduleName = substr($package, 7);
                    $analysis->drupalModules[$moduleName] = $version;
                }
                
                if ($package === 'drupal/core-recommended') {
                    $analysis->drupalVersion = $version;
                }
                
                if ($package === 'php') {
                    $analysis->phpVersion = $version;
                }
                
                if ($package === 'drupal/upgrade_status') {
                    $analysis->hasUpgradeStatus = true;
                }
            }
        }

        return $analysis;
    }

    public function scanCustomModules(string $modulesPath): array
    {
        $modules = [];
        
        if (!$this->filesystem->exists($modulesPath)) {
            return $modules;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($modulesPath)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'info' || $file->getExtension() === 'yml') {
                if (str_ends_with($file->getFilename(), '.info.yml')) {
                    $moduleName = str_replace('.info.yml', '', $file->getFilename());
                    $moduleInfo = $this->parseModuleInfo($file->getPathname());
                    
                    $module = new ModuleInfo(
                        name: $moduleName,
                        version: $moduleInfo['version'] ?? 'dev',
                        type: 'custom',
                        d11Status: $this->determineCustomModuleStatus($file->getPath(), $moduleInfo),
                        deprecatedFunctions: $this->scanForDeprecatedFunctions($file->getPath())
                    );
                    
                    $modules[] = $module;
                }
            }
        }

        return $modules;
    }

    public function checkPhpCompatibility(string $codebasePath): PhpCompatibility
    {
        $composerPath = $codebasePath . '/composer.json';
        $analysis = $this->analyzeComposer($composerPath);
        
        $currentVersion = $analysis->phpVersion ?? 'unknown';
        $isCompatible = version_compare($currentVersion, '8.3', '>=');
        
        $issues = [];
        if (!$isCompatible) {
            $issues[] = 'PHP version needs updating to 8.3+ for Drupal 11';
        }

        return new PhpCompatibility(
            currentVersion: $currentVersion,
            isCompatible: $isCompatible,
            issues: $issues
        );
    }

    public function scanForDeprecatedFunctions(string $path): array
    {
        $deprecatedFound = [];
        
        if (!$this->filesystem->exists($path)) {
            return $deprecatedFound;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                
                foreach ($this->deprecatedFunctions as $function) {
                    if (strpos($content, $function) !== false) {
                        $deprecatedFound[] = [
                            'function' => $function,
                            'file' => $file->getPathname(),
                            'line' => $this->findLineNumber($content, $function)
                        ];
                    }
                }
            }
        }

        return $deprecatedFound;
    }

    private function parseModuleInfo(string $infoPath): array
    {
        $content = file_get_contents($infoPath);
        $info = [];
        
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            if (strpos($line, ':') !== false) {
                [$key, $value] = explode(':', $line, 2);
                $info[trim($key)] = trim($value);
            }
        }

        return $info;
    }

    private function determineCustomModuleStatus(string $modulePath, array $moduleInfo): CompatibilityStatus
    {
        $coreVersion = $moduleInfo['core_version_requirement'] ?? $moduleInfo['core'] ?? '';
        
        if (strpos($coreVersion, '^11') !== false || strpos($coreVersion, '11.') !== false) {
            return CompatibilityStatus::COMPATIBLE;
        }
        
        if (strpos($coreVersion, '^10') !== false) {
            return CompatibilityStatus::UPDATE_REQUIRED;
        }

        return CompatibilityStatus::UNKNOWN;
    }

    private function findLineNumber(string $content, string $search): ?int
    {
        $lines = explode("\n", $content);
        foreach ($lines as $lineNumber => $line) {
            if (strpos($line, $search) !== false) {
                return $lineNumber + 1;
            }
        }
        return null;
    }

    public function analyzeContribModule(string $moduleName, string $version): ModuleInfo
    {
        if (isset($this->removeModules[$moduleName])) {
            return new ModuleInfo(
                name: $moduleName,
                version: $version,
                type: 'contrib',
                d11Status: CompatibilityStatus::REMOVE,
                removeReason: $this->removeModules[$moduleName]
            );
        }

        return new ModuleInfo(
            name: $moduleName,
            version: $version,
            type: 'contrib',
            d11Status: CompatibilityStatus::UNKNOWN
        );
    }
}