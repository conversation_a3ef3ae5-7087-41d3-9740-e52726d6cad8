<?php

namespace <PERSON><PERSON>al\Evaluator\Command;

use <PERSON><PERSON><PERSON>\Evaluator\Analyzer\StaticAnalyzer;
use <PERSON>upal\Evaluator\Data\AnalysisResult;
use Drupal\Evaluator\Generator\ReportGenerator;
use Drupal\Evaluator\Handler\RepositoryHandler;
use Drupal\Evaluator\Manager\EnvironmentManager;
use Drupal\Evaluator\Scanner\UpgradeStatusScanner;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Filesystem\Filesystem;

#[AsCommand(
    name: 'analyze',
    description: 'Analyze a Drupal site for D11 readiness'
)]
class AnalyzeCommand extends Command
{
    private RepositoryHandler $repositoryHandler;
    private StaticAnalyzer $staticAnalyzer;
    private EnvironmentManager $environmentManager;
    private ReportGenerator $reportGenerator;
    private Filesystem $filesystem;

    public function __construct()
    {
        parent::__construct();

        $this->repositoryHandler = new RepositoryHandler();
        $this->staticAnalyzer = new StaticAnalyzer();
        $this->environmentManager = new EnvironmentManager();
        $this->reportGenerator = new ReportGenerator();
        $this->filesystem = new Filesystem();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('source', InputArgument::REQUIRED, 'Git repository URL or local path')
            ->addOption('static-only', 's', InputOption::VALUE_NONE, 'Perform static analysis only (no Lando)')
            ->addOption('custom-only', 'c', InputOption::VALUE_NONE, 'Analyze custom modules only (skip contrib modules)')
            ->addOption('output', 'o', InputOption::VALUE_OPTIONAL, 'Output file path')
            ->addOption('format', 'f', InputOption::VALUE_OPTIONAL, 'Output format (markdown|json)', 'markdown')
            ->addOption('work-dir', 'w', InputOption::VALUE_OPTIONAL, 'Working directory for cloned repos', sys_get_temp_dir());
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $source = $input->getArgument('source');
        $staticOnly = $input->getOption('static-only');
        $customOnly = $input->getOption('custom-only');
        $outputFile = $input->getOption('output');
        $format = $input->getOption('format');
        $workDir = $input->getOption('work-dir');

        $io->title('Drupal 11 Readiness Evaluator');

        $projectPath = $this->prepareProject($source, $workDir, $io);
        if (!$projectPath) {
            $io->error('Failed to prepare project for analysis');
            return Command::FAILURE;
        }

        try {
            $analysis = $this->performAnalysis($projectPath, $staticOnly, $customOnly, $io);
            $report = $this->reportGenerator->export($analysis, $format);

            if ($outputFile) {
                file_put_contents($outputFile, $report);
                $io->success("Report saved to: {$outputFile}");
            } else {
                $output->writeln($report);
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error("Analysis failed: " . $e->getMessage());
            return Command::FAILURE;
        } finally {
            if (str_starts_with($projectPath, sys_get_temp_dir())) {
                $this->filesystem->remove($projectPath);
            }
        }
    }

    private function prepareProject(string $source, string $workDir, SymfonyStyle $io): ?string
    {
        if (filter_var($source, FILTER_VALIDATE_URL)) {
            $io->section('Cloning repository');
            $targetDir = $workDir . '/drupal11-eval-' . uniqid();

            if (!$this->repositoryHandler->clone($source, $targetDir)) {
                return null;
            }

            $io->success("Repository cloned to: {$targetDir}");
            return $targetDir;
        } else {
            if (!$this->filesystem->exists($source)) {
                $io->error("Path does not exist: {$source}");
                return null;
            }

            return $source;
        }
    }

    private function performAnalysis(string $projectPath, bool $staticOnly, bool $customOnly, SymfonyStyle $io): AnalysisResult
    {
        $io->section('Analyzing repository structure');

        $repoInfo = $this->repositoryHandler->analyze($projectPath);
        if (!$repoInfo->isValid) {
            throw new \RuntimeException('Not a valid Drupal repository');
        }

        $io->success('Valid Drupal repository detected');

        $io->section('Performing static analysis');

        $composerAnalysis = $this->staticAnalyzer->analyzeComposer($repoInfo->composerPath);
        $phpCompatibility = $this->staticAnalyzer->checkPhpCompatibility($projectPath);

        $allModules = [];
        $customModules = [];

        // Only analyze contrib modules if not in custom-only mode
        if (!$customOnly) {
            foreach ($composerAnalysis->drupalModules as $name => $version) {
                $module = $this->staticAnalyzer->analyzeContribModule($name, $version);
                $allModules[] = $module;
            }
        }

        // Always analyze custom modules
        foreach ($repoInfo->customModulePaths as $modulePath) {
            $modules = $this->staticAnalyzer->scanCustomModules($modulePath);
            $customModules = array_merge($customModules, $modules);
        }

        if ($customOnly) {
            $io->success(sprintf('Static analysis complete: %d custom modules', count($customModules)));
        } else {
            $io->success(sprintf(
                'Static analysis complete: %d contrib modules, %d custom modules',
                count($allModules),
                count($customModules)
            ));
        }

        $analysis = new AnalysisResult(
            drupalVersion: $composerAnalysis->drupalVersion ?? 'unknown',
            phpVersion: $phpCompatibility->currentVersion,
            modules: $allModules,
            customModules: $customModules
        );

        if (!$staticOnly) {
            $io->section('Checking Lando environment');

            // Debug: Show the project path being analyzed
            $io->text("Analyzing project path: {$projectPath}");

            // Debug: Check if .lando.yml exists
            $landoFile = $projectPath . '/.lando.yml';
            if (file_exists($landoFile)) {
                $io->text("✓ Found .lando.yml file");
            } else {
                $io->text("✗ No .lando.yml file found");
            }

            // Debug: Check if Lando is running
            $io->text("Checking if Lando is running...");
            if ($this->environmentManager->isLandoRunning($projectPath)) {
                $io->text("✓ Lando is running");
            } else {
                $io->text("✗ Lando is not running");
            }

            // Debug: Try to run drush status
            $io->text("Testing Drupal accessibility via drush...");
            $drushProcess = new \Symfony\Component\Process\Process(['lando', 'drush', 'status'], $projectPath);
            $drushProcess->setTimeout(60);
            $drushProcess->run();

            if ($drushProcess->isSuccessful()) {
                $io->text("✓ Drupal is accessible via drush");
                $io->text("Drush output: " . trim($drushProcess->getOutput()));
            } else {
                $io->text("✗ Drupal is not accessible via drush");
                $io->text("Error: " . trim($drushProcess->getErrorOutput()));
            }

            if (!$this->environmentManager->setupLando($projectPath)) {
                $io->error('Lando environment not ready. Please set up manually:');
                $instructions = $this->environmentManager->getSetupInstructions();
                foreach ($instructions as $instruction) {
                    $io->text("  {$instruction}");
                }
                $io->text('Then run the evaluator again on the local directory.');
                throw new \RuntimeException('Lando environment setup failed');
            }

            if (!$this->environmentManager->hasDatabase($projectPath)) {
                $io->warning('No database detected - you may want to run ./vits_copy_live first');
                $io->text('Continuing with available data...');
            }

            $io->success('Lando environment validated');

            $io->section('Installing upgrade_status module');

            if ($this->environmentManager->installUpgradeStatus()) {
                $io->success('upgrade_status module installed');

                $io->section('Running upgrade_status scans');
                $scanner = new UpgradeStatusScanner($projectPath, $output);

                // Only scan contrib modules if not in custom-only mode
                if (!$customOnly) {
                    $contribReport = $scanner->scanContribModules();
                    $analysis->modules = $this->mergeContribResults($analysis->modules, $contribReport);
                }
                
                // Always scan custom modules
                $customReport = $scanner->scanCustomModules();
                $analysis->customModules = $customReport->modules;

                $io->success('upgrade_status scans complete');
            } else {
                $io->warning('Failed to install upgrade_status - using static analysis results');
            }

            // Don't stop Lando - user manages it
        }

        return $this->finalizeAnalysis($analysis);
    }

    private function mergeContribResults(array $staticModules, $contribReport): array
    {
        $merged = [];
        $reportModules = array_merge(
            $contribReport->compatible,
            $contribReport->updateRequired,
            $contribReport->majorUpdateRequired,
            $contribReport->patchAvailable,
            $contribReport->remove,
            $contribReport->problematic
        );

        foreach ($staticModules as $staticModule) {
            $found = false;
            foreach ($reportModules as $reportModule) {
                if ($staticModule->name === $reportModule->name) {
                    $merged[] = $reportModule;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $merged[] = $staticModule;
            }
        }

        return $merged;
    }

    private function finalizeAnalysis(AnalysisResult $analysis): AnalysisResult
    {
        $modulesByStatus = $analysis->getModulesByStatus();
        $totalModules = count($analysis->modules);
        $problematicCount = count($modulesByStatus['problematic'] ?? []) +
                          count($modulesByStatus['major_update_required'] ?? []);

        if ($problematicCount > $totalModules * 0.3) {
            $analysis->complexity = 'High';
            $analysis->estimatedHours = 40 + ($problematicCount * 2);
        } elseif ($problematicCount > $totalModules * 0.1) {
            $analysis->complexity = 'Medium';
            $analysis->estimatedHours = 20 + ($problematicCount * 1);
        } else {
            $analysis->complexity = 'Low';
            $analysis->estimatedHours = 8 + ($problematicCount * 0.5);
        }

        if (!empty($modulesByStatus['problematic'])) {
            foreach ($modulesByStatus['problematic'] as $module) {
                $analysis->criticalBlockers[] = "{$module->name} - no D11 version available";
            }
        }

        if (!version_compare($analysis->phpVersion, '8.3', '>=')) {
            $analysis->riskFactors[] = 'PHP version requires update to 8.3+';
        }

        return $analysis;
    }
}
