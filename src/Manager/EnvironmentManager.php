<?php

namespace Drupal\Evaluator\Manager;

use Drupal\Evaluator\Interface\EnvironmentManagerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Process\Process;

class EnvironmentManager implements EnvironmentManagerInterface
{
    private Filesystem $filesystem;

    public function __construct()
    {
        $this->filesystem = new Filesystem();
    }

    public function setupLando(string $projectPath): bool
    {
        if (!$this->filesystem->exists($projectPath)) {
            return false;
        }

        $landoFile = $projectPath . '/.lando.yml';
        if (!$this->filesystem->exists($landoFile)) {
            return false; // Require manual setup
        }

        // Just validate existing setup
        return $this->validateLandoSetup($projectPath);
    }

    public function updateDrupal(string $targetVersion): bool
    {
        $process = new Process(['composer', 'require', "drupal/core-recommended:^{$targetVersion}"]);
        $process->setTimeout(600);
        $process->run();

        if (!$process->isSuccessful()) {
            return false;
        }

        $process = new Process(['composer', 'require', "drupal/core-dev:^{$targetVersion}", '--dev']);
        $process->setTimeout(600);
        $process->run();

        return $process->isSuccessful();
    }

    public function installUpgradeStatus(): bool
    {
        $process = new Process(['composer', 'require', 'drupal/upgrade_status']);
        $process->setTimeout(300);
        $process->run();

        if (!$process->isSuccessful()) {
            return false;
        }

        $process = new Process(['lando', 'drush', 'en', 'upgrade_status', '-y']);
        $process->setTimeout(120);
        $process->run();

        return $process->isSuccessful();
    }

    public function isLandoRunning(string $projectPath): bool
    {
        $process = new Process(['lando', 'info'], $projectPath);
        $process->run();

        // Debug output for troubleshooting
        if (!$process->isSuccessful()) {
            error_log("Lando info failed. Exit code: " . $process->getExitCode());
            error_log("Error output: " . $process->getErrorOutput());
            error_log("Standard output: " . $process->getOutput());
        }

        return $process->isSuccessful();
    }

    public function startLando(string $projectPath): bool
    {
        $process = new Process(['lando', 'start'], $projectPath);
        $process->setTimeout(600);
        $process->run();

        return $process->isSuccessful();
    }

    public function stopLando(string $projectPath): bool
    {
        $process = new Process(['lando', 'stop'], $projectPath);
        $process->setTimeout(120);
        $process->run();

        return $process->isSuccessful();
    }

    public function runDrushCommand(string $projectPath, array $command): Process
    {
        $fullCommand = array_merge(['lando', 'drush'], $command);
        $process = new Process($fullCommand, $projectPath);
        $process->setTimeout(300);
        $process->run();

        return $process;
    }

    public function updatePhpVersion(string $landoFile, string $phpVersion = '8.3'): bool
    {
        if (!$this->filesystem->exists($landoFile)) {
            return false;
        }

        $content = file_get_contents($landoFile);
        $yaml = yaml_parse($content);

        if (!$yaml || !isset($yaml['services'])) {
            return false;
        }

        if (isset($yaml['services']['appserver'])) {
            $yaml['services']['appserver']['type'] = "php:{$phpVersion}";
        }

        $newContent = yaml_emit($yaml);
        file_put_contents($landoFile, $newContent);

        return true;
    }

    private function createBasicLandoFile(string $landoFile): void
    {
        $defaultConfig = [
            'name' => 'drupal11-evaluation',
            'recipe' => 'drupal9',
            'config' => [
                'php' => '8.3',
                'database' => 'mysql:8.0',
                'webroot' => 'web'
            ],
            'services' => [
                'appserver' => [
                    'type' => 'php:8.3'
                ],
                'database' => [
                    'type' => 'mysql:8.0'
                ]
            ]
        ];

        $content = yaml_emit($defaultConfig);
        file_put_contents($landoFile, $content);
    }

    private function updateLandoForD11(string $landoFile): bool
    {
        $content = file_get_contents($landoFile);
        $yaml = yaml_parse($content);

        if (!$yaml) {
            return false;
        }

        $yaml['recipe'] = 'drupal10';
        
        if (!isset($yaml['config'])) {
            $yaml['config'] = [];
        }
        
        $yaml['config']['php'] = '8.3';
        $yaml['config']['database'] = 'mysql:8.0';

        if (!isset($yaml['services'])) {
            $yaml['services'] = [];
        }

        $yaml['services']['appserver']['type'] = 'php:8.3';
        $yaml['services']['database']['type'] = 'mysql:8.0';

        $newContent = yaml_emit($yaml);
        file_put_contents($landoFile, $newContent);

        return true;
    }

    public function validateLandoSetup(string $projectPath): bool
    {
        // Check if Lando is running
        if (!$this->isLandoRunning($projectPath)) {
            error_log("validateLandoSetup: Lando is not running for path: {$projectPath}");
            return false;
        }

        // Check if Drupal is accessible
        $process = new Process(['lando', 'drush', 'status'], $projectPath);
        $process->setTimeout(60);
        $process->run();

        if (!$process->isSuccessful()) {
            error_log("validateLandoSetup: Drush status failed. Exit code: " . $process->getExitCode());
            error_log("validateLandoSetup: Error output: " . $process->getErrorOutput());
            error_log("validateLandoSetup: Standard output: " . $process->getOutput());
        }

        return $process->isSuccessful();
    }

    public function hasDatabase(string $projectPath): bool
    {
        $process = new Process(['lando', 'drush', 'sql:query', 'SHOW TABLES'], $projectPath);
        $process->setTimeout(30);
        $process->run();

        if (!$process->isSuccessful()) {
            return false;
        }

        $output = $process->getOutput();
        return !empty(trim($output));
    }

    public function getSetupInstructions(): array
    {
        return [
            '1. Clone the repository to a local directory',
            '2. Run ./vits_setup (if available)',
            '3. Edit .lando.yml to ensure unique site name',
            '4. Run: lando start',
            '5. Run ./vits_copy_live (if available) to get fresh database',
            '6. Run the evaluator on the local directory'
        ];
    }
}
