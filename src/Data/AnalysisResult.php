<?php

namespace Drupal\Evaluator\Data;

class AnalysisResult
{
    public function __construct(
        public string $drupalVersion,
        public string $phpVersion,
        public ?string $drushVersion = null,
        public ?string $databaseInfo = null,
        public array $modules = [],
        public array $customModules = [],
        public string $complexity = 'unknown',
        public int $estimatedHours = 0,
        public array $criticalBlockers = [],
        public array $riskFactors = []
    ) {}

    public function getModulesByStatus(): array
    {
        $grouped = [];
        foreach ($this->modules as $module) {
            $status = $module->d11Status->value;
            if (!isset($grouped[$status])) {
                $grouped[$status] = [];
            }
            $grouped[$status][] = $module;
        }
        return $grouped;
    }

    public function toArray(): array
    {
        return [
            'drupal_version' => $this->drupalVersion,
            'php_version' => $this->phpVersion,
            'drush_version' => $this->drushVersion,
            'database_info' => $this->databaseInfo,
            'modules' => array_map(fn($m) => $m->toArray(), $this->modules),
            'custom_modules' => array_map(fn($m) => $m->toArray(), $this->customModules),
            'complexity' => $this->complexity,
            'estimated_hours' => $this->estimatedHours,
            'critical_blockers' => $this->criticalBlockers,
            'risk_factors' => $this->riskFactors
        ];
    }
}