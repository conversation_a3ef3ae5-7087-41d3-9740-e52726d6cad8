<?php

namespace Drupal\Evaluator\Data;

class CustomModuleReport
{
    public function __construct(
        public array $modules = [],
        public int $totalCount = 0,
        public int $readyCount = 0,
        public int $needsWorkCount = 0
    ) {}

    public function toArray(): array
    {
        return [
            'modules' => array_map(fn($m) => $m->toArray(), $this->modules),
            'total_count' => $this->totalCount,
            'ready_count' => $this->readyCount,
            'needs_work_count' => $this->needsWorkCount
        ];
    }
}