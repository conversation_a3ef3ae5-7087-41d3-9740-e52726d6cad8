<?php

namespace Drupal\Evaluator\Data;

class PhpCompatibility
{
    public function __construct(
        public string $currentVersion,
        public string $requiredVersion = '8.3',
        public bool $isCompatible = false,
        public array $issues = []
    ) {}

    public function toArray(): array
    {
        return [
            'current_version' => $this->currentVersion,
            'required_version' => $this->requiredVersion,
            'is_compatible' => $this->isCompatible,
            'issues' => $this->issues
        ];
    }
}