<?php

namespace Drupal\Evaluator\Data;

class PatchInfo
{
    public function __construct(
        public string $url,
        public string $description,
        public bool $isReviewed = false,
        public bool $isTested = false,
        public string $quality = 'unknown' // unknown|poor|good|excellent
    ) {}

    public function toArray(): array
    {
        return [
            'url' => $this->url,
            'description' => $this->description,
            'is_reviewed' => $this->isReviewed,
            'is_tested' => $this->isTested,
            'quality' => $this->quality
        ];
    }
}