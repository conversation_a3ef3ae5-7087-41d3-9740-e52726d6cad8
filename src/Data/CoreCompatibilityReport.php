<?php

namespace Drupal\Evaluator\Data;

class CoreCompatibilityReport
{
    public function __construct(
        public string $currentVersion,
        public string $latestVersion,
        public bool $isCompatible = false,
        public array $issues = []
    ) {}

    public function toArray(): array
    {
        return [
            'current_version' => $this->currentVersion,
            'latest_version' => $this->latestVersion,
            'is_compatible' => $this->isCompatible,
            'issues' => $this->issues
        ];
    }
}