<?php

namespace Drupal\Evaluator\Data;

class RepositoryInfo
{
    public function __construct(
        public string $path,
        public bool $isValid = false,
        public ?string $drupalRoot = null,
        public ?string $composerPath = null,
        public ?string $landoPath = null,
        public array $customModulePaths = [],
        public array $customThemePaths = []
    ) {}

    public function toArray(): array
    {
        return [
            'path' => $this->path,
            'is_valid' => $this->isValid,
            'drupal_root' => $this->drupalRoot,
            'composer_path' => $this->composerPath,
            'lando_path' => $this->landoPath,
            'custom_module_paths' => $this->customModulePaths,
            'custom_theme_paths' => $this->customThemePaths
        ];
    }
}