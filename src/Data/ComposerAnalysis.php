<?php

namespace Drupal\Evaluator\Data;

class ComposerAnalysis
{
    public function __construct(
        public array $drupalModules = [],
        public ?string $drupalVersion = null,
        public ?string $phpVersion = null,
        public array $dependencies = [],
        public bool $hasUpgradeStatus = false
    ) {}

    public function toArray(): array
    {
        return [
            'drupal_modules' => $this->drupalModules,
            'drupal_version' => $this->drupalVersion,
            'php_version' => $this->phpVersion,
            'dependencies' => $this->dependencies,
            'has_upgrade_status' => $this->hasUpgradeStatus
        ];
    }
}