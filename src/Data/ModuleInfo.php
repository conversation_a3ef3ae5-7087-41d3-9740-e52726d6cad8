<?php

namespace Drupal\Evaluator\Data;

use Drupal\Evaluator\CompatibilityStatus;

class ModuleInfo
{
    public function __construct(
        public string $name,
        public string $version,
        public string $type, // contrib|custom|core
        public CompatibilityStatus $d11Status,
        public ?string $d11Version = null,
        public array $issues = [],
        public ?PatchInfo $patch = null,
        public int $complexityScore = 0,
        public ?string $removeReason = null,
        public array $deprecatedFunctions = []
    ) {}

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'version' => $this->version,
            'type' => $this->type,
            'd11_status' => $this->d11Status->value,
            'd11_version' => $this->d11Version,
            'issues' => $this->issues,
            'patch' => $this->patch?->toArray(),
            'complexity_score' => $this->complexityScore,
            'remove_reason' => $this->removeReason,
            'deprecated_functions' => $this->deprecatedFunctions
        ];
    }
}