<?php

namespace Drupal\Evaluator\Data;

class ContribModuleReport
{
    public function __construct(
        public array $compatible = [],
        public array $updateRequired = [],
        public array $majorUpdateRequired = [],
        public array $patchAvailable = [],
        public array $remove = [],
        public array $problematic = []
    ) {}

    public function toArray(): array
    {
        return [
            'compatible' => array_map(fn($m) => $m->toArray(), $this->compatible),
            'update_required' => array_map(fn($m) => $m->toArray(), $this->updateRequired),
            'major_update_required' => array_map(fn($m) => $m->toArray(), $this->majorUpdateRequired),
            'patch_available' => array_map(fn($m) => $m->toArray(), $this->patchAvailable),
            'remove' => array_map(fn($m) => $m->toArray(), $this->remove),
            'problematic' => array_map(fn($m) => $m->toArray(), $this->problematic)
        ];
    }

    public function getTotalCount(): int
    {
        return count($this->compatible) + count($this->updateRequired) + 
               count($this->majorUpdateRequired) + count($this->patchAvailable) + 
               count($this->remove) + count($this->problematic);
    }
}