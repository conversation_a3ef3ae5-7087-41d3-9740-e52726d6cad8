<?php

namespace Drupal\Evaluator\Handler;

use <PERSON>upal\Evaluator\Data\RepositoryInfo;
use Drupal\Evaluator\Interface\RepositoryHandlerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Process\Process;

class RepositoryHandler implements RepositoryHandlerInterface
{
    private Filesystem $filesystem;

    public function __construct()
    {
        $this->filesystem = new Filesystem();
    }

    public function clone(string $repoUrl, string $targetDir): bool
    {
        if ($this->filesystem->exists($targetDir)) {
            $this->filesystem->remove($targetDir);
        }

        $process = new Process(['git', 'clone', $repoUrl, $targetDir]);
        $process->setTimeout(300);
        $process->run();

        return $process->isSuccessful();
    }

    public function analyze(string $path): RepositoryInfo
    {
        $info = new RepositoryInfo($path);
        
        if (!$this->filesystem->exists($path)) {
            return $info;
        }

        $info->isValid = $this->isValidDrupalRepo($path);
        
        if ($info->isValid) {
            $info->drupalRoot = $this->findDrupalRoot($path);
            $info->composerPath = $this->findComposerJson($path);
            $info->landoPath = $this->findLandoYml($path);
            $info->customModulePaths = $this->findCustomModules($path);
            $info->customThemePaths = $this->findCustomThemes($path);
        }

        return $info;
    }

    public function isValidDrupalRepo(string $path): bool
    {
        $indicators = [
            'composer.json',
            'web/index.php',
            'docroot/index.php',
            'index.php'
        ];

        foreach ($indicators as $indicator) {
            if ($this->filesystem->exists($path . '/' . $indicator)) {
                return true;
            }
        }

        return false;
    }

    private function findDrupalRoot(string $path): ?string
    {
        $possibleRoots = [
            $path . '/web',
            $path . '/docroot',
            $path
        ];

        foreach ($possibleRoots as $root) {
            if ($this->filesystem->exists($root . '/index.php') && 
                $this->filesystem->exists($root . '/core')) {
                return $root;
            }
        }

        return null;
    }

    private function findComposerJson(string $path): ?string
    {
        $composerPath = $path . '/composer.json';
        return $this->filesystem->exists($composerPath) ? $composerPath : null;
    }

    private function findLandoYml(string $path): ?string
    {
        $landoFiles = ['.lando.yml', '.lando.yaml'];
        
        foreach ($landoFiles as $file) {
            $landoPath = $path . '/' . $file;
            if ($this->filesystem->exists($landoPath)) {
                return $landoPath;
            }
        }

        return null;
    }

    private function findCustomModules(string $path): array
    {
        $possiblePaths = [
            $path . '/web/modules/custom',
            $path . '/docroot/modules/custom',
            $path . '/modules/custom'
        ];

        $customModulePaths = [];
        foreach ($possiblePaths as $modulePath) {
            if ($this->filesystem->exists($modulePath)) {
                $customModulePaths[] = $modulePath;
            }
        }

        return $customModulePaths;
    }

    private function findCustomThemes(string $path): array
    {
        $possiblePaths = [
            $path . '/web/themes/custom',
            $path . '/docroot/themes/custom',
            $path . '/themes/custom'
        ];

        $customThemePaths = [];
        foreach ($possiblePaths as $themePath) {
            if ($this->filesystem->exists($themePath)) {
                $customThemePaths[] = $themePath;
            }
        }

        return $customThemePaths;
    }
}