<?php

namespace Drupal\Evaluator\Scanner;

use <PERSON><PERSON><PERSON>\Evaluator\CompatibilityStatus;
use <PERSON><PERSON>al\Evaluator\Data\ContribModuleReport;
use Drupal\Evaluator\Data\CoreCompatibilityReport;
use Drupal\Evaluator\Data\CustomModuleReport;
use Drupal\Evaluator\Data\ModuleInfo;
use Drupal\Evaluator\Interface\UpgradeStatusScannerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Process\Process;

class UpgradeStatusScanner implements UpgradeStatusScannerInterface
{
    private string $projectPath;
    private ?OutputInterface $output;

    public function __construct(string $projectPath, ?OutputInterface $output = null)
    {
        $this->projectPath = $projectPath;
        $this->output = $output;
    }

    public function scanContribModules(): ContribModuleReport
    {
        $process = new Process([
            'lando', 'drush', 'upgrade_status:analyze', '--all', '--ignore-custom', '--ignore-uninstalled'//, '--skip-existing'
        ], $this->projectPath);

        $process->setTimeout(300);

        if ($this->output) {
            $this->output->writeln('<info>Running: ' . $process->getCommandLine() . '</info>');
            $this->output->writeln('<comment>Analyzing contrib modules (this may take several minutes)...</comment>');
        } else {
            // Debug: Check if output is null
            error_log('UpgradeStatusScanner: No output interface provided for contrib scan');
        }

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output) {
                // Stream output in real-time, with type prefix for debugging
                if (Process::ERR === $type) {
                    $this->output->write('<error>' . $buffer . '</error>');
                } else {
                    $this->output->write($buffer);
                }
            }
        });

        if (!$process->isSuccessful()) {
            if ($this->output) {
                $this->output->writeln('<error>Contrib module scan failed with exit code: ' . $process->getExitCode() . '</error>');
                $this->output->writeln('<error>Error output: ' . $process->getErrorOutput() . '</error>');
            }
            return new ContribModuleReport();
        }

        if ($this->output) {
            $this->output->writeln('<info>Contrib module scan completed successfully</info>');
        }

        return $this->parseContribResults($outputBuffer);
    }

    public function scanCustomModules(): CustomModuleReport
    {
        $process = new Process([
            'lando', 'drush', 'upgrade_status:analyze', '--all', '--ignore-contrib', '--ignore-uninstalled'//, '--skip-existing'
        ], $this->projectPath);

        $process->setTimeout(300);

        if ($this->output) {
            $this->output->writeln('<info>Running: ' . $process->getCommandLine() . '</info>');
            $this->output->writeln('<comment>Analyzing custom modules (this may take several minutes)...</comment>');
        }

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output) {
                // Stream output in real-time, with type prefix for debugging
                if (Process::ERR === $type) {
                    $this->output->write('<error>' . $buffer . '</error>');
                } else {
                    $this->output->write($buffer);
                }
            }
        });

        if (!$process->isSuccessful()) {
            if ($this->output) {
                $this->output->writeln('<error>Custom module scan failed with exit code: ' . $process->getExitCode() . '</error>');
                $this->output->writeln('<error>Error output: ' . $process->getErrorOutput() . '</error>');
            }
            return new CustomModuleReport();
        }

        if (!$outputBuffer) {
            if ($this->output) {
                $this->output->writeln('<warning>No output from custom module scan</warning>');
            }
            return new CustomModuleReport();
        }

        if ($this->output) {
            $this->output->writeln('<info>Custom module scan completed successfully</info>');
        }

        return $this->parseCustomResults($outputBuffer);
    }

    public function scanCore(): CoreCompatibilityReport
    {
        $process = new Process([
            'lando', 'drush', 'status', '--format=json'
        ], $this->projectPath);
        
        $process->run();

        if (!$process->isSuccessful()) {
            return new CoreCompatibilityReport('unknown', '11.0');
        }

        $status = json_decode($process->getOutput(), true);
        $currentVersion = $status['drupal-version'] ?? 'unknown';

        return new CoreCompatibilityReport(
            currentVersion: $currentVersion,
            latestVersion: '11.0',
            isCompatible: version_compare($currentVersion, '11.0', '>=')
        );
    }

    public function getUpgradeStatusData(): array
    {
        $process = new Process([
            'lando', 'drush', 'upgrade_status:analyze', '--all', '--format=json'
        ], $this->projectPath);

        $process->setTimeout(300);

        if ($this->output) {
            $this->output->writeln('<info>Running: ' . $process->getCommandLine() . '</info>');
            $this->output->writeln('<comment>Getting upgrade status data in JSON format...</comment>');
        }

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output) {
                // For JSON output, we might not want to stream it as it could be messy
                // Just show progress dots
                $this->output->write('.');
            }
        });

        if (!$process->isSuccessful()) {
            if ($this->output) {
                $this->output->writeln('');
                $this->output->writeln('<error>Upgrade status data retrieval failed with exit code: ' . $process->getExitCode() . '</error>');
            }
            return [];
        }

        if ($this->output) {
            $this->output->writeln('');
            $this->output->writeln('<info>Upgrade status data retrieved successfully</info>');
        }

        return json_decode($outputBuffer, true) ?? [];
    }

    private function parseContribResults(string $output): ContribModuleReport
    {
        $report = new ContribModuleReport();
        $lines = explode("\n", trim($output));
        
        foreach ($lines as $line) {
            if (empty(trim($line)) || str_starts_with($line, '[')) {
                continue;
            }

            $module = $this->parseModuleLine($line);
            if ($module) {
                switch ($module->d11Status) {
                    case CompatibilityStatus::COMPATIBLE:
                        $report->compatible[] = $module;
                        break;
                    case CompatibilityStatus::UPDATE_REQUIRED:
                        $report->updateRequired[] = $module;
                        break;
                    case CompatibilityStatus::MAJOR_UPDATE_REQUIRED:
                        $report->majorUpdateRequired[] = $module;
                        break;
                    case CompatibilityStatus::PATCH_AVAILABLE:
                        $report->patchAvailable[] = $module;
                        break;
                    case CompatibilityStatus::REMOVE:
                        $report->remove[] = $module;
                        break;
                    case CompatibilityStatus::PROBLEMATIC:
                        $report->problematic[] = $module;
                        break;
                }
            }
        }

        return $report;
    }

    private function parseCustomResults(string $output): CustomModuleReport
    {
        $modules = [];
        $lines = explode("\n", trim($output));
        
        $readyCount = 0;
        $needsWorkCount = 0;
        
        foreach ($lines as $line) {
            if (empty(trim($line)) || str_starts_with($line, '[')) {
                continue;
            }

            $module = $this->parseModuleLine($line);
            if ($module && $module->type === 'custom') {
                $modules[] = $module;
                
                if ($module->d11Status === CompatibilityStatus::COMPATIBLE) {
                    $readyCount++;
                } else {
                    $needsWorkCount++;
                }
            }
        }

        return new CustomModuleReport(
            modules: $modules,
            totalCount: count($modules),
            readyCount: $readyCount,
            needsWorkCount: $needsWorkCount
        );
    }

    private function parseModuleLine(string $line): ?ModuleInfo
    {
        if (preg_match('/^\s*(\S+)\s+(.+)$/', trim($line), $matches)) {
            $moduleName = $matches[1];
            $description = $matches[2];
            
            $status = $this->determineStatusFromDescription($description);
            $type = str_contains($description, 'custom') ? 'custom' : 'contrib';

            return new ModuleInfo(
                name: $moduleName,
                version: $this->extractVersion($description),
                type: $type,
                d11Status: $status,
                issues: $this->extractIssues($description)
            );
        }

        return null;
    }

    private function determineStatusFromDescription(string $description): CompatibilityStatus
    {
        $lower = strtolower($description);
        
        if (str_contains($lower, 'compatible') || str_contains($lower, 'ready')) {
            return CompatibilityStatus::COMPATIBLE;
        }
        
        if (str_contains($lower, 'update available') || str_contains($lower, 'newer version')) {
            return str_contains($lower, 'major') ? 
                CompatibilityStatus::MAJOR_UPDATE_REQUIRED : 
                CompatibilityStatus::UPDATE_REQUIRED;
        }
        
        if (str_contains($lower, 'patch available')) {
            return CompatibilityStatus::PATCH_AVAILABLE;
        }
        
        if (str_contains($lower, 'remove') || str_contains($lower, 'deprecated')) {
            return CompatibilityStatus::REMOVE;
        }
        
        if (str_contains($lower, 'no update') || str_contains($lower, 'problematic')) {
            return CompatibilityStatus::PROBLEMATIC;
        }

        return CompatibilityStatus::UNKNOWN;
    }

    private function extractVersion(string $description): string
    {
        if (preg_match('/(\d+\.\d+\.\d+)/', $description, $matches)) {
            return $matches[1];
        }
        
        return 'unknown';
    }

    private function extractIssues(string $description): array
    {
        $issues = [];
        
        if (str_contains(strtolower($description), 'deprecated')) {
            $issues[] = 'Contains deprecated functions';
        }
        
        if (str_contains(strtolower($description), 'api changes')) {
            $issues[] = 'API changes required';
        }

        return $issues;
    }
}