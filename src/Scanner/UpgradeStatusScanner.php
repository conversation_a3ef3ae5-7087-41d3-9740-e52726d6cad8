<?php

namespace Dr<PERSON>al\Evaluator\Scanner;

use <PERSON><PERSON><PERSON>\Evaluator\CompatibilityStatus;
use <PERSON>upal\Evaluator\Data\ContribModuleReport;
use Drupal\Evaluator\Data\CoreCompatibilityReport;
use Drupal\Evaluator\Data\CustomModuleReport;
use Drupal\Evaluator\Data\ModuleInfo;
use Drupal\Evaluator\Interface\UpgradeStatusScannerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Process\Process;

class UpgradeStatusScanner implements UpgradeStatusScannerInterface
{
    private string $projectPath;
    private ?OutputInterface $output;

    public function __construct(string $projectPath, ?OutputInterface $output = null)
    {
        $this->projectPath = $projectPath;
        $this->output = $output;
    }

    public function scanContribModules(): ContribModuleReport
    {
        if ($this->output) {
            $this->output->writeln('<info>Getting list of enabled contrib modules...</info>');
        }

        $enabledModules = $this->getEnabledModules();
        $contribModules = array_filter($enabledModules, fn($module) => $module['type'] === 'contrib');

        if ($this->output) {
            $this->output->writeln('<info>Found ' . count($contribModules) . ' enabled contrib modules</info>');
        }

        $report = new ContribModuleReport();

        foreach ($contribModules as $moduleName => $moduleData) {
            if ($this->output) {
                $this->output->writeln('<comment>Analyzing ' . $moduleName . '...</comment>');
            }

            $moduleInfo = $this->analyzeModule($moduleName, $moduleData);
            if ($moduleInfo) {
                switch ($moduleInfo->d11Status) {
                    case CompatibilityStatus::COMPATIBLE:
                        $report->compatible[] = $moduleInfo;
                        break;
                    case CompatibilityStatus::UPDATE_REQUIRED:
                        $report->updateRequired[] = $moduleInfo;
                        break;
                    case CompatibilityStatus::MAJOR_UPDATE_REQUIRED:
                        $report->majorUpdateRequired[] = $moduleInfo;
                        break;
                    case CompatibilityStatus::PATCH_AVAILABLE:
                        $report->patchAvailable[] = $moduleInfo;
                        break;
                    case CompatibilityStatus::REMOVE:
                        $report->remove[] = $moduleInfo;
                        break;
                    case CompatibilityStatus::PROBLEMATIC:
                        $report->problematic[] = $moduleInfo;
                        break;
                }
            }
        }

        if ($this->output) {
            $this->output->writeln('<info>Contrib module analysis completed</info>');
        }

        return $report;
    }

    public function scanCustomModules(): CustomModuleReport
    {
        if ($this->output) {
            $this->output->writeln('<info>Getting list of enabled custom modules...</info>');
        }

        $enabledModules = $this->getEnabledModules();
        $customModules = array_filter($enabledModules, fn($module) => $module['type'] === 'custom');

        if ($this->output) {
            $this->output->writeln('<info>Found ' . count($customModules) . ' enabled custom modules</info>');
        }

        $modules = [];
        $readyCount = 0;
        $needsWorkCount = 0;

        foreach ($customModules as $moduleName => $moduleData) {
            if ($this->output) {
                $this->output->writeln('<comment>Analyzing ' . $moduleName . '...</comment>');
            }

            $moduleInfo = $this->analyzeModule($moduleName, $moduleData);
            if ($moduleInfo && $moduleInfo->type === 'custom') {
                $modules[] = $moduleInfo;

                if ($moduleInfo->d11Status === CompatibilityStatus::COMPATIBLE) {
                    $readyCount++;
                } else {
                    $needsWorkCount++;
                }
            }
        }

        if ($this->output) {
            $this->output->writeln('<info>Custom module analysis completed</info>');
        }

        return new CustomModuleReport(
            modules: $modules,
            totalCount: count($modules),
            readyCount: $readyCount,
            needsWorkCount: $needsWorkCount
        );
    }

    public function scanCore(): CoreCompatibilityReport
    {
        $process = new Process([
            'lando', 'drush', 'status', '--format=json'
        ], $this->projectPath);
        
        $process->run();

        if (!$process->isSuccessful()) {
            return new CoreCompatibilityReport('unknown', '11.0');
        }

        $status = json_decode($process->getOutput(), true);
        $currentVersion = $status['drupal-version'] ?? 'unknown';

        return new CoreCompatibilityReport(
            currentVersion: $currentVersion,
            latestVersion: '11.0',
            isCompatible: version_compare($currentVersion, '11.0', '>=')
        );
    }

    public function getUpgradeStatusData(): array
    {
        $process = new Process([
            'lando', 'drush', 'upgrade_status:analyze', '--all', '--format=json'
        ], $this->projectPath);

        $process->setTimeout(300);

        if ($this->output) {
            $this->output->writeln('<info>Running: ' . $process->getCommandLine() . '</info>');
            $this->output->writeln('<comment>Getting upgrade status data in JSON format...</comment>');
        }

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output) {
                // For JSON output, we might not want to stream it as it could be messy
                // Just show progress dots
                $this->output->write('.');
            }
        });

        if (!$process->isSuccessful()) {
            if ($this->output) {
                $this->output->writeln('');
                $this->output->writeln('<error>Upgrade status data retrieval failed with exit code: ' . $process->getExitCode() . '</error>');
            }
            return [];
        }

        if ($this->output) {
            $this->output->writeln('');
            $this->output->writeln('<info>Upgrade status data retrieved successfully</info>');
        }

        return json_decode($outputBuffer, true) ?? [];
    }

    private function getEnabledModules(): array
    {
        $process = new Process([
            'lando', 'drush', 'pm:list', '--format=json', '--no-core'
        ], $this->projectPath);

        $process->setTimeout(60);

        if ($this->output) {
            $this->output->writeln('<info>Getting list of enabled modules...</info>');
        }

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output && Process::ERR === $type) {
                $this->output->write('<error>' . $buffer . '</error>');
            }
        });

        if (!$process->isSuccessful()) {
            if ($this->output) {
                $this->output->writeln('<error>Failed to get module list: ' . $process->getErrorOutput() . '</error>');
            }
            return [];
        }

        $moduleData = json_decode($outputBuffer, true) ?? [];
        $enabledModules = [];

        foreach ($moduleData as $moduleName => $info) {
            // Only include enabled modules
            if (isset($info['status']) && $info['status'] === 'Enabled') {
                $enabledModules[$moduleName] = [
                    'version' => $info['version'] ?? 'unknown',
                    'type' => $this->determineModuleType($moduleName, $info),
                    'package' => $info['package'] ?? 'Other'
                ];
            }
        }

        return $enabledModules;
    }

    private function determineModuleType(string $moduleName, array $moduleInfo): string
    {
        // Check if it's in a custom path or has custom indicators
        if (isset($moduleInfo['path'])) {
            if (str_contains($moduleInfo['path'], '/custom/') ||
                str_contains($moduleInfo['path'], 'modules/custom') ||
                str_contains($moduleInfo['path'], 'themes/custom')) {
                return 'custom';
            }
        }

        // Check package name for custom indicators
        if (isset($moduleInfo['package']) &&
            (str_contains(strtolower($moduleInfo['package']), 'custom') ||
             $moduleInfo['package'] === 'Other')) {
            return 'custom';
        }

        return 'contrib';
    }

    private function analyzeModule(string $moduleName, array $moduleData): ?ModuleInfo
    {
        $process = new Process([
            'lando', 'drush', 'upgrade_status:analyze', $moduleName, '--format=codeclimate'
        ], $this->projectPath);

        $process->setTimeout(120);

        $outputBuffer = '';
        $process->run(function ($type, $buffer) use (&$outputBuffer) {
            $outputBuffer .= $buffer;

            if ($this->output && Process::ERR === $type) {
                $this->output->write('<error>' . $buffer . '</error>');
            }
        });

        // If no output or command failed, assume module is D11 ready
        if (!$process->isSuccessful() || empty(trim($outputBuffer))) {
            if ($this->output) {
                $this->output->writeln('<info>' . $moduleName . ' appears to be Drupal 11 ready (no issues found)</info>');
            }

            return new ModuleInfo(
                name: $moduleName,
                version: $moduleData['version'],
                type: $moduleData['type'],
                d11Status: CompatibilityStatus::COMPATIBLE,
                issues: []
            );
        }

        // Parse the CodeClimate JSON output
        $analysisData = json_decode($outputBuffer, true);
        if (!$analysisData || !is_array($analysisData)) {
            if ($this->output) {
                $this->output->writeln('<warning>' . $moduleName . ' analysis returned invalid JSON, assuming compatible</warning>');
            }

            return new ModuleInfo(
                name: $moduleName,
                version: $moduleData['version'],
                type: $moduleData['type'],
                d11Status: CompatibilityStatus::COMPATIBLE,
                issues: []
            );
        }

        return $this->parseCodeClimateResults($moduleName, $moduleData, $analysisData);
    }

    private function parseCodeClimateResults(string $moduleName, array $moduleData, array $analysisData): ModuleInfo
    {
        $issues = [];
        $status = CompatibilityStatus::COMPATIBLE;

        foreach ($analysisData as $issue) {
            if (isset($issue['description'])) {
                $issues[] = $issue['description'];

                // Determine severity based on issue content
                $description = strtolower($issue['description']);
                if (str_contains($description, 'deprecated') ||
                    str_contains($description, 'removed') ||
                    str_contains($description, 'fatal')) {
                    $status = CompatibilityStatus::PROBLEMATIC;
                } elseif ($status === CompatibilityStatus::COMPATIBLE) {
                    $status = CompatibilityStatus::UPDATE_REQUIRED;
                }
            }
        }

        if ($this->output) {
            $issueCount = count($issues);
            if ($issueCount > 0) {
                $this->output->writeln('<warning>' . $moduleName . ' has ' . $issueCount . ' compatibility issues</warning>');
            } else {
                $this->output->writeln('<info>' . $moduleName . ' is compatible</info>');
            }
        }

        return new ModuleInfo(
            name: $moduleName,
            version: $moduleData['version'],
            type: $moduleData['type'],
            d11Status: $status,
            issues: $issues
        );
    }

    private function parseContribResults(string $output): ContribModuleReport
    {
        $report = new ContribModuleReport();
        $lines = explode("\n", trim($output));
        
        foreach ($lines as $line) {
            if (empty(trim($line)) || str_starts_with($line, '[')) {
                continue;
            }

            $module = $this->parseModuleLine($line);
            if ($module) {
                switch ($module->d11Status) {
                    case CompatibilityStatus::COMPATIBLE:
                        $report->compatible[] = $module;
                        break;
                    case CompatibilityStatus::UPDATE_REQUIRED:
                        $report->updateRequired[] = $module;
                        break;
                    case CompatibilityStatus::MAJOR_UPDATE_REQUIRED:
                        $report->majorUpdateRequired[] = $module;
                        break;
                    case CompatibilityStatus::PATCH_AVAILABLE:
                        $report->patchAvailable[] = $module;
                        break;
                    case CompatibilityStatus::REMOVE:
                        $report->remove[] = $module;
                        break;
                    case CompatibilityStatus::PROBLEMATIC:
                        $report->problematic[] = $module;
                        break;
                }
            }
        }

        return $report;
    }

    private function parseCustomResults(string $output): CustomModuleReport
    {
        $modules = [];
        $lines = explode("\n", trim($output));
        
        $readyCount = 0;
        $needsWorkCount = 0;
        
        foreach ($lines as $line) {
            if (empty(trim($line)) || str_starts_with($line, '[')) {
                continue;
            }

            $module = $this->parseModuleLine($line);
            if ($module && $module->type === 'custom') {
                $modules[] = $module;
                
                if ($module->d11Status === CompatibilityStatus::COMPATIBLE) {
                    $readyCount++;
                } else {
                    $needsWorkCount++;
                }
            }
        }

        return new CustomModuleReport(
            modules: $modules,
            totalCount: count($modules),
            readyCount: $readyCount,
            needsWorkCount: $needsWorkCount
        );
    }

    private function parseModuleLine(string $line): ?ModuleInfo
    {
        if (preg_match('/^\s*(\S+)\s+(.+)$/', trim($line), $matches)) {
            $moduleName = $matches[1];
            $description = $matches[2];
            
            $status = $this->determineStatusFromDescription($description);
            $type = str_contains($description, 'custom') ? 'custom' : 'contrib';

            return new ModuleInfo(
                name: $moduleName,
                version: $this->extractVersion($description),
                type: $type,
                d11Status: $status,
                issues: $this->extractIssues($description)
            );
        }

        return null;
    }

    private function determineStatusFromDescription(string $description): CompatibilityStatus
    {
        $lower = strtolower($description);
        
        if (str_contains($lower, 'compatible') || str_contains($lower, 'ready')) {
            return CompatibilityStatus::COMPATIBLE;
        }
        
        if (str_contains($lower, 'update available') || str_contains($lower, 'newer version')) {
            return str_contains($lower, 'major') ? 
                CompatibilityStatus::MAJOR_UPDATE_REQUIRED : 
                CompatibilityStatus::UPDATE_REQUIRED;
        }
        
        if (str_contains($lower, 'patch available')) {
            return CompatibilityStatus::PATCH_AVAILABLE;
        }
        
        if (str_contains($lower, 'remove') || str_contains($lower, 'deprecated')) {
            return CompatibilityStatus::REMOVE;
        }
        
        if (str_contains($lower, 'no update') || str_contains($lower, 'problematic')) {
            return CompatibilityStatus::PROBLEMATIC;
        }

        return CompatibilityStatus::UNKNOWN;
    }

    private function extractVersion(string $description): string
    {
        if (preg_match('/(\d+\.\d+\.\d+)/', $description, $matches)) {
            return $matches[1];
        }
        
        return 'unknown';
    }

    private function extractIssues(string $description): array
    {
        $issues = [];
        
        if (str_contains(strtolower($description), 'deprecated')) {
            $issues[] = 'Contains deprecated functions';
        }
        
        if (str_contains(strtolower($description), 'api changes')) {
            $issues[] = 'API changes required';
        }

        return $issues;
    }
}