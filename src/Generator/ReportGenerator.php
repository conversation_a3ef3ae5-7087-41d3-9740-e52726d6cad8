<?php

namespace Drupal\Evaluator\Generator;

use Dr<PERSON>al\Evaluator\CompatibilityStatus;
use <PERSON><PERSON>al\Evaluator\Data\AnalysisResult;
use Dr<PERSON>al\Evaluator\Interface\ReportGeneratorInterface;

class ReportGenerator implements ReportGeneratorInterface
{
    public function generate(AnalysisResult $analysis): string
    {
        return $this->generateMarkdown($analysis);
    }

    public function export(AnalysisResult $analysis, string $format): string
    {
        return match ($format) {
            'json' => $this->generateJson($analysis),
            'markdown', 'md' => $this->generateMarkdown($analysis),
            default => throw new \InvalidArgumentException("Unsupported format: {$format}")
        };
    }

    public function generateJson(AnalysisResult $analysis): string
    {
        return json_encode($analysis->toArray(), JSON_PRETTY_PRINT);
    }

    public function generateMarkdown(AnalysisResult $analysis): string
    {
        $report = "# Upgrade Audit\n\n";
        $report .= "**Drupal version:** {$analysis->drupalVersion}\n\n";
        
        $report .= "## Environment\n\n";
        $report .= $this->generateEnvironmentSection($analysis);
        
        $report .= "\n## Modules Analysis\n\n";
        $report .= $this->generateModulesSection($analysis);
        
        if (!empty($analysis->customModules)) {
            $report .= "\n## Custom Modules: " . count($analysis->customModules) . "\n\n";
            $report .= $this->generateCustomModulesSection($analysis->customModules);
        }
        
        $report .= "\n## Overall Assessment\n\n";
        $report .= $this->generateAssessmentSection($analysis);
        
        return $report;
    }

    private function generateEnvironmentSection(AnalysisResult $analysis): string
    {
        $section = "";
        
        $section .= "**PHP version:** {$analysis->phpVersion}";
        if (!version_compare($analysis->phpVersion, '8.3', '>=')) {
            $section .= " - Needs updating to 8.3";
        }
        $section .= "\n\n";
        
        if ($analysis->drushVersion) {
            $section .= "**Drush version:** {$analysis->drushVersion}";
            if (!version_compare($analysis->drushVersion, '13', '>=')) {
                $section .= " - Needs updating to 13";
            }
            $section .= "\n\n";
        }
        
        if ($analysis->databaseInfo) {
            $section .= "**Database:** {$analysis->databaseInfo}\n\n";
        }
        
        return $section;
    }

    private function generateModulesSection(AnalysisResult $analysis): string
    {
print "here\n";
        $modulesByStatus = $analysis->getModulesByStatus();
        $section = "";
        
        if (isset($modulesByStatus[CompatibilityStatus::COMPATIBLE->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::COMPATIBLE->value]);
            $section .= "**Already compatible:** {$count}\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::COMPATIBLE->value] as $module) {
                $section .= "- {$module->name} ({$module->version})\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::REMOVE->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::REMOVE->value]);
            $section .= "**Remove:** {$count}\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::REMOVE->value] as $module) {
                $section .= "- **{$module->name}** - {$module->removeReason}\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::UPDATE_REQUIRED->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::UPDATE_REQUIRED->value]);
            $section .= "**Update to newer D11 compatible version:** {$count}\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::UPDATE_REQUIRED->value] as $module) {
                $section .= "- {$module->name} ({$module->version})";
                if ($module->d11Version) {
                    $section .= " → {$module->d11Version}";
                }
                $section .= "\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::MAJOR_UPDATE_REQUIRED->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::MAJOR_UPDATE_REQUIRED->value]);
            $section .= "**Major version changes:** {$count} - extra testing required\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::MAJOR_UPDATE_REQUIRED->value] as $module) {
                $section .= "- {$module->name} ({$module->version})";
                if ($module->d11Version) {
                    $section .= " → {$module->d11Version}";
                }
                if (!empty($module->issues)) {
                    $section .= " - " . implode(', ', $module->issues);
                }
                $section .= "\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::PATCH_AVAILABLE->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::PATCH_AVAILABLE->value]);
            $section .= "**Patch for D11:** {$count}\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::PATCH_AVAILABLE->value] as $module) {
                $section .= "- **{$module->name}** - ";
                if ($module->patch) {
                    $section .= "patch available";
                    if ($module->patch->isReviewed && $module->patch->isTested) {
                        $section .= " - has been reviewed & tested by the community";
                    }
                }
                $section .= "\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::PROBLEMATIC->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::PROBLEMATIC->value]);
            $section .= "**Problematic:** {$count}\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::PROBLEMATIC->value] as $module) {
                $section .= "- **{$module->name}** - ";
                if (!empty($module->issues)) {
                    $section .= implode(', ', $module->issues);
                } else {
                    $section .= "hasn't officially been updated since D9";
                }
                $section .= "\n";
            }
            $section .= "\n";
        }
        
        if (isset($modulesByStatus[CompatibilityStatus::UNKNOWN->value])) {
            $count = count($modulesByStatus[CompatibilityStatus::UNKNOWN->value]);
            $section .= "**Status unknown:** {$count} - requires manual review\n\n";
            foreach ($modulesByStatus[CompatibilityStatus::UNKNOWN->value] as $module) {
                $section .= "- {$module->name} ({$module->version})\n";
            }
            $section .= "\n";
        }
        
        return $section;
    }

    private function generateCustomModulesSection(array $customModules): string
    {
        $section = "";
        $readyCount = 0;
        
        foreach ($customModules as $module) {
            if ($module->d11Status === CompatibilityStatus::COMPATIBLE) {
                $readyCount++;
            }
        }
        
        $section .= "scan claims {$readyCount} are already ok after adding \"^11\" to them\n\n";
        
        foreach ($customModules as $module) {
            $section .= "- **{$module->name}**";
            
            if ($module->d11Status === CompatibilityStatus::COMPATIBLE) {
                $section .= " - already OK";
            } else {
                $section .= " - needs ^11 added";
                
                if (!empty($module->deprecatedFunctions)) {
                    $funcCount = count($module->deprecatedFunctions);
                    $functions = array_unique(array_column($module->deprecatedFunctions, 'function'));
                    $section .= ", ({$funcCount}x) " . implode(', ', $functions);
                }
            }
            
            $section .= "\n";
        }
        
        return $section;
    }

    private function generateAssessmentSection(AnalysisResult $analysis): string
    {
        $section = "";
        
        $section .= "- **Complexity:** {$analysis->complexity}\n";
        
        if ($analysis->estimatedHours > 0) {
            $section .= "- **Estimated effort:** {$analysis->estimatedHours} hours\n";
        }
        
        if (!empty($analysis->criticalBlockers)) {
            $section .= "- **Critical blockers:** " . count($analysis->criticalBlockers) . "\n";
            foreach ($analysis->criticalBlockers as $blocker) {
                $section .= "  - {$blocker}\n";
            }
        }
        
        if (!empty($analysis->riskFactors)) {
            $section .= "- **Risk factors:**\n";
            foreach ($analysis->riskFactors as $risk) {
                $section .= "  - {$risk}\n";
            }
        }
        
        return $section;
    }
}