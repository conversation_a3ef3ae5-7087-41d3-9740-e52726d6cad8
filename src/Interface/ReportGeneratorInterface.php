<?php

namespace Drupal\Evaluator\Interface;

use Drupal\Evaluator\Data\AnalysisResult;

interface ReportGeneratorInterface
{
    public function generate(AnalysisResult $analysis): string;
    
    public function export(AnalysisResult $analysis, string $format): string;
    
    public function generateJson(AnalysisResult $analysis): string;
    
    public function generateMarkdown(AnalysisResult $analysis): string;
}