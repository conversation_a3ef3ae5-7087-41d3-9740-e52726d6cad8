<?php

namespace Dr<PERSON>al\Evaluator\Interface;

use Drupal\Evaluator\Data\ContribModuleReport;
use Drupal\Evaluator\Data\CustomModuleReport;
use Drupal\Evaluator\Data\CoreCompatibilityReport;

interface UpgradeStatusScannerInterface
{
    public function scanContribModules(): ContribModuleReport;
    
    public function scanCustomModules(): CustomModuleReport;
    
    public function scanCore(): CoreCompatibilityReport;
    
    public function getUpgradeStatusData(): array;
}