<?php

namespace Drupal\Evaluator\Interface;

interface EnvironmentManagerInterface
{
    public function setupLando(string $projectPath): bool;
    
    public function updateDrupal(string $targetVersion): bool;
    
    public function installUpgradeStatus(): bool;
    
    public function isLandoRunning(string $projectPath): bool;
    
    public function startLando(string $projectPath): bool;
    
    public function stopLando(string $projectPath): bool;
}