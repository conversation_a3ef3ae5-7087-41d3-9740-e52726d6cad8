<?php

namespace Dr<PERSON>al\Evaluator\Interface;

use <PERSON><PERSON><PERSON>\Evaluator\Data\ComposerAnalysis;
use Drupal\Evaluator\Data\PhpCompatibility;

interface StaticAnalyzerInterface
{
    public function analyzeComposer(string $composerPath): ComposerAnalysis;
    
    public function scanCustomModules(string $modulesPath): array;
    
    public function checkPhpCompatibility(string $codebasePath): PhpCompatibility;
    
    public function scanForDeprecatedFunctions(string $path): array;
}